import { But<PERSON> } from "@workspace/ui/components/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { Separator } from "@workspace/ui/components/separator";
import Image from "next/image";

interface Bank {
  name: string;
  image: string;
}

interface BankDepositDialogProps {
  banks: Bank[];
}

export async function BankDepositDialog({ banks }: BankDepositDialogProps) {
  return (
    <Dialog open>
      <form>
        <DialogContent className="sm:max-w-[512px]">
          <DialogHeader>
            <DialogTitle>{"Havale ile Para Yatır"}</DialogTitle>
          </DialogHeader>
          <div className="grid grid-cols-8 gap-4 mx-2">
            <div className="flex flex-col gap-2 col-span-3 h-64 p-2 overflow-auto scrollbar">
              <div className="space-y-3">
                {banks.map((bank) => (
                  <button
                    key={bank.name}
                    className="cursor-pointer border-3 rounded-sm overflow-hidden border-background ring-foreground ring-2 w-full h-20 relative"
                  >
                    <Image
                      src={bank.image}
                      alt={bank.name}
                      fill
                      className="object-cover"
                    />
                  </button>
                ))}
              </div>
            </div>
            <div className="mt-2 col-span-5 bg-muted rounded-lg flex flex-col">
              <div className="p-4 overflow-hidden">
                <span className="inline-block bg-foreground py-2 px-4 skew-x-24">
                  <span className="-skew-x-24 text-xs text-background text-shadow-none ">
                    {"HESAP ADI"}
                  </span>
                </span>
              </div>
            </div>
          </div>
          <Separator />
          <DialogFooter>
            <div className="flex gap-5 items-center mx-4 -mt-5 mb-4 max-w-sm">
              <div className="shrink-0 text-background border-destructive border inset-ring-ring inset-ring-2 size-5 rotate-45 bg-destructive flex items-center justify-center drop-shadow-sm drop-shadow-border/25">
                <span className="text-shadow-none -rotate-45 text-xs">!</span>
              </div>
              <p className="text-muted-foreground text-xs">
                {
                  "Sadece kendi adınıza kayıtlı banka hesaplarından yapılan gönderimler kabul edilecektir."
                }
              </p>
            </div>
          </DialogFooter>
        </DialogContent>
      </form>
    </Dialog>
  );
}
