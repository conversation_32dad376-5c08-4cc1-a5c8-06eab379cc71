import { BankDepositDialog } from "@/components/bank-deposit.dialog";

import halkbank from "./banks/halkbank.jpg";
import isbank from "./banks/isbankasi.jpg";
import ziraat from "./banks/ziraatbankasi.jpg";
import yapikredi from "./banks/yapikredi.jpg";
import garanti from "./banks/garanti.jpg";
import vakifbank from "./banks/vakifbank.jpg";

export default function Page() {
  return (
    <BankDepositDialog
      banks={[
        { name: "Yapı Kredi", image: yapikredi.src },
        { name: "<PERSON><PERSON><PERSON>", image: garanti.src },
        { name: "İş Bankası", image: isbank.src },
        { name: "Vakıfbank", image: vakifbank.src },
        { name: "Halkbank", image: halkbank.src },
        { name: "Ziraat Bankası", image: ziraat.src },
      ]}
    />
  );
}
